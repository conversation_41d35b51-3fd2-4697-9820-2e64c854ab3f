﻿// ============= Clash 64GB内存优化配置脚本 =============
// 版本: 64GB Memory Optimized v3.0
// 优化目标: 充分利用64GB大内存，提供最佳性能和稳定性
//
// 主要优化特性:
// 1. 内存管理: 大幅增加缓存大小，减少清理频率，关闭激进GC
// 2. 连接管理: 增加连接池大小，延长空闲超时，启用连接复用
// 3. 健康检查: 适度增加检查间隔，平衡性能和稳定性
// 4. DNS优化: 增加DNS缓存时间，启用DNS预取，提高响应速度
// 5. 监控清理: 智能内存监控，延长清理间隔，启用性能日志
// 6. 性能优先: 在64GB内存基础上，优先考虑性能和响应速度
// 7. 缓冲区优化: 大幅增加各种缓冲区大小，减少I/O瓶颈
// 8. 并发优化: 增加并发连接数和流数，提高并发处理能力
// =====================================================

// 国内DNS服务器
const domesticNameservers = [
  "https://*********/dns-query", // 阿里DoH
  "https://doh.pub/dns-query" // 腾讯DoH，因腾讯云即将关闭免费版IP访问，故用域名
];

// ============= 订阅获取代理配置 =============
// 设置获取订阅时使用的代理，可选值：
// "DIRECT" - 直连获取
// "⚡ 深港专线" - 通过深港专线获取
// "🏠 家宽" - 通过家宽节点获取
// "🇺🇸 美国" - 通过美国节点获取
// 注意：避免使用依赖订阅的代理组，可能造成循环依赖
const SUBSCRIPTION_PROXY = "DIRECT";

// ============= 落地节点配置开关 =============
// 设置为 true 启用落地节点功能，设置为 false 禁用
const ENABLE_LANDING_NODES = true;

// 落地节点配置
const landingNodeConfig = {
  server: "**************",
  port: 5067,
  username: "bmfofebu",
  password: "1v5sfkbfsoml"
};
// ========================================

// 国外DNS服务器
const foreignNameservers = [
  "https://doh.pub/dns-query",                    // 腾讯 DoH (国内)
  "https://dns.alidns.com/dns-query",             // 阿里 DoH (国内)
  "https://*******/dns-query",                    // Cloudflare 通常最快
  "https://*******/dns-query#ecs=***************/24&ecs-override=true", // GoogleDNS
  "https://cloudflare-dns.com/dns-query", // CloudflareDNS
  "https://*********/dns-query", //YandexDNS
  "https://**************/dns-query#ecs=*******/24&ecs-override=true", // OpenDNS
  "https://*******/dns-query", //Quad9DNS
];
// DNS配置 - 长期运行优化，AI服务友好
const dnsConfig = {
  "enable": true,
  "listen": "0.0.0.0:1053",
  "ipv6": true,
  "prefer-h3": false,
  "respect-rules": true,
  "use-system-hosts": true,           // 启用系统hosts，提高解析速度
  "cache-algorithm": "arc",
  "concurrent": 2,                     // ✅ 修复：改为2个并发，减少资源消耗
  "fallback-delay": 200,               // ✅ 修复：改为200ms，更保守的回退
  "cache-ttl": 1200,                   // ✅ 修复：改为20分钟，平衡更新和稳定
  "enhanced-mode": "fake-ip",
  "fake-ip-range": "**********/16",
  "fake-ip-cache-size": 32768,         // ✅ 修复：改为32K，减少内存占用
  "fake-ip-filter": [
    // 本地主机/设备
    "+.lan",
    "+.local",
    // Windows网络出现小地球图标
    "+.msftconnecttest.com",
    "+.msftncsi.com",
    // QQ快速登录检测失败
    "localhost.ptlogin2.qq.com",
    "localhost.sec.qq.com",
    // 微信快速登录检测失败
    "localhost.work.weixin.qq.com",
    // Cursor 相关域名（避免fake-ip影响）
    "+.cursor.sh",
    "+.cursor.com",
    "+.api.cursor.sh",
    "+.api.cursor.com",
    "+.auth.cursor.sh",
    "+.auth.cursor.com",
    "+.marketplace.cursorapi.com",
    "+.cursor-cdn.com",
    "+.elevenlabs.io",
    "+.download.todesktop.com",
    // ✅ 新增：AI服务DNS过滤，确保不使用fake-ip
    "+.openai.com",
    "+.api.openai.com",
    "+.claude.ai",
    "+.api.claude.ai",
    "+.gemini.google.com",
    // 游戏相关
    "+.srv.nintendo.net",
    "+.stun.playstation.net",
    "+.xboxlive.com",
    "xbox.*.microsoft.com",
    "*.battlenet.com.cn",
    "*.battlenet.com",
    "*.blzstatic.cn",
    "*.battle.net"
  ],
  "default-nameserver": [
    "*******",           // Google DNS
    "*******",           // Cloudflare DNS
    "************",      // 腾讯DNSPod
    "*********",         // 阿里DNS
    "*********",         // 阿里DNS
    "************"       // 百度DNS
  ],
  "nameserver": [...foreignNameservers],
  "proxy-server-nameserver": [
    "https://*******/dns-query",          // Cloudflare DNS
    "https://*******/dns-query",          // Google DNS
    "https://doh.pub/dns-query",           // 腾讯
    "https://dns.alidns.com/dns-query",    // 阿里
    "https://*******/dns-query",           // Quad9
    "https://**************/dns-query"     // OpenDNS
  ],
  "direct-nameserver": [
    "system",
    "https://doh.pub/dns-query",          // 腾讯
    "https://dns.alidns.com/dns-query",   // 阿里
    "114.114.114.114",                    // 114
    "114.114.115.115",                    // 114
    "101.226.4.6",                        // 百度-电信
    "123.125.81.6",                       // 百度-联通
    "************",                       // 百度-联通
    "112.124.47.27",                      // OneDNS-南方
    "114.215.126.16"                      // OneDNS-北方
  ],
  "direct-nameserver-follow-policy": false,
  "nameserver-policy": {
    "geosite:cn": domesticNameservers
  }
};

// 规则集通用配置 - 长期运行优化
const ruleProviderCommon = {
  "type": "http",
  "format": "yaml",
  "interval": 86400,                   // 长期运行优化：延长更新频率到24小时
  "timeout": 30000,                    // 增加超时时间到30秒
  "retry": 2                           // 添加重试机制
};
// 规则集配置
const ruleProviders = {
  "reject": {
    ...ruleProviderCommon,
    "behavior": "domain",
    "url": "https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/reject.txt",
    "path": "./ruleset/loyalsoldier/reject.yaml"
  },
  "icloud": {
    ...ruleProviderCommon,
    "behavior": "domain",
    "url": "https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/icloud.txt",
    "path": "./ruleset/loyalsoldier/icloud.yaml"
  },
  "apple": {
    ...ruleProviderCommon,
    "behavior": "domain",
    "url": "https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/apple.txt",
    "path": "./ruleset/loyalsoldier/apple.yaml"
  },
  "gemini": {
    ...ruleProviderCommon,
    "behavior": "classical",
    "url": "https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/Gemini/Gemini.yaml",
    "path": "./ruleset/blackmatrix7/gemini.yaml"
  },
  "google": {
    ...ruleProviderCommon,
    "behavior": "domain",
    "url": "https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/google.txt",
    "path": "./ruleset/loyalsoldier/google.yaml"
  },
  "proxy": {
    ...ruleProviderCommon,
    "behavior": "domain",
    "url": "https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/proxy.txt",
    "path": "./ruleset/loyalsoldier/proxy.yaml"
  },
  "direct": {
    ...ruleProviderCommon,
    "behavior": "domain",
    "url": "https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/direct.txt",
    "path": "./ruleset/loyalsoldier/direct.yaml"
  },
  "private": {
    ...ruleProviderCommon,
    "behavior": "domain",
    "url": "https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/private.txt",
    "path": "./ruleset/loyalsoldier/private.yaml"
  },
  "gfw": {
    ...ruleProviderCommon,
    "behavior": "domain",
    "url": "https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/gfw.txt",
    "path": "./ruleset/loyalsoldier/gfw.yaml"
  },
  "tld-not-cn": {
    ...ruleProviderCommon,
    "behavior": "domain",
    "url": "https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/tld-not-cn.txt",
    "path": "./ruleset/loyalsoldier/tld-not-cn.yaml"
  },
  "telegramcidr": {
    ...ruleProviderCommon,
    "behavior": "ipcidr",
    "url": "https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/telegramcidr.txt",
    "path": "./ruleset/loyalsoldier/telegramcidr.yaml"
  },
  "cncidr": {
    ...ruleProviderCommon,
    "behavior": "ipcidr",
    "url": "https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/cncidr.txt",
    "path": "./ruleset/loyalsoldier/cncidr.yaml"
  },
  "lancidr": {
    ...ruleProviderCommon,
    "behavior": "ipcidr",
    "url": "https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/lancidr.txt",
    "path": "./ruleset/loyalsoldier/lancidr.yaml"
  },
  "applications": {
    ...ruleProviderCommon,
    "behavior": "classical",
    "url": "https://fastly.jsdelivr.net/gh/Loyalsoldier/clash-rules@release/applications.txt",
    "path": "./ruleset/loyalsoldier/applications.yaml"
  },
  // blackmatrix7 规则集
  "advertising": {
    ...ruleProviderCommon,
    "behavior": "classical",
    "url": "https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/AdvertisingLite/AdvertisingLite_Classical.yaml",
    "path": "./ruleset/blackmatrix7/advertising.yaml"
  },
  "copilot": {
    ...ruleProviderCommon,
    "behavior": "classical",
    "url": "https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/Copilot/Copilot.yaml",
    "path": "./ruleset/blackmatrix7/copilot.yaml"
  },
  "github": {
    ...ruleProviderCommon,
    "behavior": "classical",
    "url": "https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/GitHub/GitHub.yaml",
    "path": "./ruleset/blackmatrix7/github.yaml"
  },
  "google-rules": {
    ...ruleProviderCommon,
    "behavior": "classical",
    "url": "https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/Google/Google.yaml",
    "path": "./ruleset/blackmatrix7/google.yaml"
  },
  "openai": {
    ...ruleProviderCommon,
    "behavior": "classical",
    "url": "https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/OpenAI/OpenAI.yaml",
    "path": "./ruleset/blackmatrix7/openai.yaml"
  },
  "netflix": {
    ...ruleProviderCommon,
    "behavior": "classical",
    "url": "https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/Netflix/Netflix.yaml",
    "path": "./ruleset/blackmatrix7/netflix.yaml"
  },
  "microsoft": {
    ...ruleProviderCommon,
    "behavior": "classical",
    "url": "https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/Microsoft/Microsoft.yaml",
    "path": "./ruleset/blackmatrix7/microsoft.yaml"
  },
  "onedrive": {
    ...ruleProviderCommon,
    "behavior": "classical",
    "url": "https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/OneDrive/OneDrive.yaml",
    "path": "./ruleset/blackmatrix7/onedrive.yaml"
  },
  "claude": {
    ...ruleProviderCommon,
    "behavior": "classical",
    "url": "https://cdn.jsdelivr.net/gh/blackmatrix7/ios_rule_script@master/rule/Clash/Claude/Claude.yaml",
    "path": "./ruleset/blackmatrix7/claude.yaml"
  },
  "games-cn": {
    ...ruleProviderCommon,
    "behavior": "domain",
    "format": "mrs",
    "url": "https://github.com/DustinWin/ruleset_geodata/releases/download/mihomo-ruleset/games-cn.mrs",
    "path": "./ruleset/dustinwin/games-cn.mrs"
  }
};
// 规则
const rules = [
  // SSH 直连规则
  "DST-PORT,22,DIRECT",
  "IP-CIDR,************/32,DIRECT",

  // Cursor 优先规则（TUN模式优化）
  "DOMAIN-SUFFIX,cursor.sh,💻 Cursor",
  "DOMAIN-SUFFIX,cursor.com,💻 Cursor",
  "DOMAIN-SUFFIX,api.cursor.sh,💻 Cursor",
  "DOMAIN-SUFFIX,api.cursor.com,💻 Cursor",
  "DOMAIN-SUFFIX,auth.cursor.sh,💻 Cursor",
  "DOMAIN-SUFFIX,auth.cursor.com,💻 Cursor",
  "DOMAIN-SUFFIX,marketplace.cursorapi.com,💻 Cursor",
  "DOMAIN-SUFFIX,cursor-cdn.com,💻 Cursor",
  "DOMAIN-SUFFIX,download.todesktop.com,💻 Cursor",
  "DOMAIN-SUFFIX,elevenlabs.io,💻 Cursor",
  "DOMAIN-SUFFIX,api.elevenlabs.io,💻 Cursor",
  "DOMAIN-SUFFIX,cdn.cursor.sh,💻 Cursor",
  "DOMAIN-SUFFIX,assets.cursor.sh,💻 Cursor",
  "DOMAIN-KEYWORD,cursor,💻 Cursor",


  // 直连优化规则
  "DOMAIN-SUFFIX,cloudflare.com,🔗 直连",

  // 广告拦截增强
  "RULE-SET,advertising,🥰 广告拦截",
  //"DOMAIN-KEYWORD,ad,🥰 广告拦截",
  "DOMAIN-KEYWORD,analytics,🥰 广告拦截",
  "DOMAIN-KEYWORD,tracking,🥰 广告拦截",
  "DOMAIN-SUFFIX,googlesyndication.com,🥰 广告拦截",
  "DOMAIN-SUFFIX,googleadservices.com,🥰 广告拦截",

  // 直连规则
  "RULE-SET,applications,🔗 直连",
  "RULE-SET,private,🔗 直连",

  // 自定义直连规则
  "DOMAIN-SUFFIX,demo.fuclaude.com,🔗 直连",
  //"DOMAIN-KEYWORD,ping0,深港专线",
  "DOMAIN-SUFFIX,qq.com,🔗 直连",
  "DOMAIN-SUFFIX,aistudio.google.com,🎯 落地节点",
  "DOMAIN-SUFFIX,poolhub.me,🔗 直连",
  "DOMAIN-KEYWORD,qq,🔗 直连",
  "DOMAIN-KEYWORD,aizex,🔗 直连",
  "DOMAIN-KEYWORD,sacinfo,🔗 直连",
  "DOMAIN-KEYWORD,loongson,🔗 直连",
  "DOMAIN-KEYWORD,easychuan,🔗 直连",
  "DOMAIN-KEYWORD,deepseek,🔗 直连",
  "DOMAIN-KEYWORD,u-tools,🔗 直连",
  "DOMAIN-KEYWORD,webcatalog,🔗 直连",
  "DOMAIN-KEYWORD,linux.do,🔗 直连",
  //"DOMAIN-KEYWORD,notion,🔗 直连",

  // 自定义出境规则
  "DOMAIN-SUFFIX,cdn.ldstatic.com,⚙️ 自动选择",

  // Gemini
  // "DOMAIN-KEYWORD,gemini,✨ Gemini",
  "RULE-SET,gemini,✨ Gemini",
  "DOMAIN-KEYWORD,perplex,✨ Gemini",
  //"DOMAIN-KEYWORD,ugment,🎯 落地节点",

  // Copilot
  "RULE-SET,copilot,💻 Copilot",
  "DOMAIN-KEYWORD,copilot,💻 Copilot",

  // GitHub
  "RULE-SET,github,📱 GitHub",

  // Google 服务
  "RULE-SET,google-rules,📢 Google",
  "RULE-SET,google,📢 Google",

  // OpenAI
  "RULE-SET,openai,🤖 OpenAI",
  "DOMAIN-KEYWORD,openai,🤖 OpenAI",
  "DOMAIN-KEYWORD,chatgpt,🤖 OpenAI",

  // Augment 规则
  "DOMAIN-KEYWORD,augment,🚀 Augment",

  // Netflix
  "RULE-SET,netflix,🎬 Netflix",

  // Grok 规则
  "DOMAIN-SUFFIX,x.ai,🤖 Grok",
  "DOMAIN-SUFFIX,grok.x.ai,🤖 Grok",
  "DOMAIN-SUFFIX,grok-api.x.ai,🤖 Grok",
  "DOMAIN-KEYWORD,grok,🤖 Grok",

  // 游戏服务
  "RULE-SET,games-cn,🎮 游戏服务",

  // 奈飞
  "DOMAIN-KEYWORD,youtube,🎬 Netflix",
  "DOMAIN-SUFFIX,googlevideo.com,🎬 Netflix",

  // Microsoft 服务
  "RULE-SET,microsoft,Ⓜ️ Microsoft",

  // OneDrive
  "RULE-SET,onedrive,☁️ OneDrive",

  // Claude
  "RULE-SET,claude,🧠 Claude",
  "DOMAIN-KEYWORD,claude,🧠 Claude",

  // 苹果服务
  "RULE-SET,icloud,🍎 苹果服务",
  "RULE-SET,apple,🍎 苹果服务",

  // 代理规则
  "RULE-SET,proxy,🔰 模式选择",
  "RULE-SET,gfw,🔰 模式选择",
  "RULE-SET,tld-not-cn,🔰 模式选择",

  // 直连规则
  "RULE-SET,direct,🔗 直连",
  "RULE-SET,lancidr,🔗 直连,no-resolve",
  "RULE-SET,cncidr,🔗 直连,no-resolve",
  "RULE-SET,telegramcidr,📲 电报消息,no-resolve",

  // 其他规则
  "GEOIP,LAN,🔗 直连,no-resolve",
  "GEOIP,CN,🔗 直连,no-resolve",
  "MATCH,🐟 漏网之鱼"
];
// 代理组通用配置 - 长期运行优化版，差异化连接管理
const groupBaseOption = {
  "interval": 600,                   // 恢复：改为10分钟，平衡检测频率
  "timeout": 5000,                   // 恢复：改为5秒，平衡速度和稳定性
  "url": "http://www.gstatic.com/generate_204", // 使用HTTP测试
  "lazy": true,                      // 恢复：启用懒加载，减少资源消耗
  "max-failed-times": 3,             // 恢复：3次失败切换，更稳定
  "hidden": false,
  "disable-udp": false,              // 确保UDP可用
  "filter": "",                      // 默认无过滤
  "exclude-filter": "",              // 默认无排除过滤
  "tcp-keep-alive": true,            // 启用TCP保活
  "tcp-no-delay": true,              // 禁用Nagle算法，降低延迟
  "tcp-fast-open": true,             // 启用TCP Fast Open
  "connection-reuse": true,          // 启用连接复用
  "persistent-connection": false,    // ✅ 修复：通用配置关闭，AI服务单独配置
  "connection-idle-timeout": 300,    // ✅ 修复：改为5分钟，通用配置
  "expected-status": 204,            // 期望HTTP状态码
  "tolerance": 50                    // 延迟容差50ms
};

// 速率代理组配置数据 - 长期运行优化
const speedGroupData = [
  { name: "⚡ 深港专线", filter: "(?i)(2.5|1.7)", tolerance: 50, interval: 600, hidden: false, icon: "https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Magic.png", type: "url-test", strategy: "sticky-sessions", "exclude-filter": "(?i)(free|免费)", "max-failed-times": 3, "timeout": 5000, "lazy": true },
  { name: "🚄 3.0倍率", filter: "(?i)([3-9]x|[3-9]倍|倍率:[3-9]|倍率:3\\d*|高倍|超高|极速|三倍|四倍|五倍)", tolerance: 50, interval: 600, hidden: false, icon: "https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Speedtest.png", type: "url-test", strategy: "sticky-sessions", "exclude-filter": "(?i)(free|免费)" }
];

// 通用高速节点组配置数据 - 速度优先，自动选择
const regionalGroupData = [
  { name: "🇺🇸 美国", type: "url-test", strategy: "sticky-sessions", filter: "美国|US|🇺🇸", tolerance: 100, interval: 600, hidden: false, icon: "https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/United_States.png", "exclude-filter": "(?i)(free|免费|ai|专用)" },
  { name: "🏠 家宽", type: "load-balance", strategy: "sticky-sessions", filter: "(?i)(家宽|住宅|residential|home|broadband|宽带|民宅|家庭)", tolerance: 100, interval: 600, hidden: false, icon: "https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Scholar.png", "exclude-filter": "(?i)(free|免费|ai|专用)" },
  { name: "🌍 欧洲", type: "url-test", strategy: "sticky-sessions", filter: "(?i)(越|土|英|法|德|意|西|荷|瑞|奥|比|挪|瑞典|丹|芬|波|捷|匈|罗|保|希|葡|爱|冰|卢|伦敦|巴黎|柏林|罗马|马德里|阿姆斯特丹|苏黎世|维也纳|布鲁塞尔|奥斯陆|斯德哥尔摩|哥本哈根|赫尔辛基|华沙|布拉格|布达佩斯|布加勒斯特|索非亚|雅典|里斯本|都柏林|雷克雅未克)", tolerance: 100, interval: 600, hidden: false, icon: "https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Europe_Map.png", "exclude-filter": "(?i)(free|免费|ai|专用)" },
  { name: "🇭🇰 香港", type: "url-test", strategy: "sticky-sessions", filter: "香港|HK|🇭🇰", tolerance: 50, interval: 600, hidden: false, icon: "https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Hong_Kong.png", "exclude-filter": "(?i)(free|免费|2.5|1.7)" },
  { name: "🇹🇼 台湾", type: "url-test", strategy: "sticky-sessions", filter: "台湾|TW|🇹🇼", tolerance: 50, interval: 600, hidden: false, icon: "https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Taiwan.png", "exclude-filter": "(?i)(free|免费)" },
  { name: "🇯🇵 日本", type: "url-test", strategy: "sticky-sessions", filter: "日本|JP|🇯🇵", tolerance: 50, interval: 600, hidden: false, icon: "https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Japan.png", "exclude-filter": "(?i)(free|免费)" },
  { name: "🇰🇷 韩国", type: "url-test", strategy: "sticky-sessions", filter: "韩国|KR|🇰🇷", tolerance: 50, interval: 600, hidden: false, icon: "https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Korea.png", "exclude-filter": "(?i)(free|免费)" },
  { name: "🇸🇬 新加坡", type: "url-test", strategy: "sticky-sessions", filter: "新加坡|SG|🇸🇬", tolerance: 100, interval: 600, hidden: false, icon: "https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Singapore.png", "exclude-filter": "(?i)(free|免费|ai|专用)" },
];

// AI节点组配置数据 - 稳定性优先，手动选择
const regionalGroupData_AI = [
  { name: "🇺🇸 美国-AI", type: "select", strategy: "sticky-sessions", filter: "美国|US|🇺🇸", tolerance: 100, interval: 1800, hidden: false, icon: "https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/United_States.png", "exclude-filter": "(?i)(free|免费|游戏|game|netflix|流媒体)" },
  { name: "🏠 家宽-AI", type: "select", strategy: "sticky-sessions", filter: "(?i)(家宽|住宅|residential|home|broadband|宽带|民宅|家庭)", tolerance: 100, interval: 1800, hidden: false, icon: "https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Scholar.png", "exclude-filter": "(?i)(free|免费)" },
  { name: "🌍 欧洲-AI", type: "select", strategy: "sticky-sessions", filter: "(?i)(越|土|英|法|德|意|西|荷|瑞|奥|比|挪|瑞典|丹|芬|波|捷|匈|罗|保|希|葡|爱|冰|卢|伦敦|巴黎|柏林|罗马|马德里|阿姆斯特丹|苏黎世|维也纳|布鲁塞尔|奥斯陆|斯德哥尔摩|哥本哈根|赫尔辛基|华沙|布拉格|布达佩斯|布加勒斯特|索非亚|雅典|里斯本|都柏林|雷克雅未克)", tolerance: 100, interval: 1800, hidden: false, icon: "https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Europe_Map.png", "exclude-filter": "(?i)(free|免费)" },
  { name: "🇭🇰 香港-AI", type: "select", strategy: "sticky-sessions", filter: "香港|HK|🇭🇰", tolerance: 50, interval: 1800, hidden: false, icon: "https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Hong_Kong.png", "exclude-filter": "(?i)(free|免费)" },
  { name: "🇹🇼 台湾-AI", type: "select", strategy: "sticky-sessions", filter: "台湾|TW|🇹🇼", tolerance: 50, interval: 1800, hidden: false, icon: "https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Taiwan.png", "exclude-filter": "(?i)(free|免费)" },
  { name: "🇯🇵 日本-AI", type: "select", strategy: "sticky-sessions", filter: "日本|JP|🇯🇵", tolerance: 50, interval: 1800, hidden: false, icon: "https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Japan.png", "exclude-filter": "(?i)(free|免费)" },
  { name: "🇰🇷 韩国-AI", type: "select", strategy: "sticky-sessions", filter: "韩国|KR|🇰🇷", tolerance: 50, interval: 1800, hidden: false, icon: "https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Korea.png", "exclude-filter": "(?i)(free|免费)" },
  { name: "🇸🇬 新加坡-AI", type: "select", strategy: "sticky-sessions", filter: "新加坡|SG|🇸🇬", tolerance: 100, interval: 1800, hidden: false, icon: "https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Singapore.png", "exclude-filter": "(?i)(free|免费|游戏|game)" },
];

// 配置提供商代理组数据 - 新增提供商只需在此处添加
// 支持各种订阅格式：Clash、V2Ray、YAML等
// 配置字段说明：
// - name: 代理组显示名称
// - provider: 内部标识符（唯一）
// - url: 订阅链接
// - prefix: 节点名称前缀
// - interval: 订阅更新间隔（秒，可选，默认86400）
// - proxy: 获取订阅时使用的代理（可选，默认DIRECT）
// - path: 缓存文件路径（可选）
// - healthCheckInterval: 健康检查间隔（秒，可选，默认300）
// - udp: 是否启用UDP（可选，默认true）
// - interfaceName: 网络接口名称（可选）
// - type: 代理组类型（可选，默认url-test）
// - strategy: 负载均衡策略（可选）
// - tolerance: 延迟容差（可选，默认50ms）
// - hidden: 是否隐藏（可选，默认false）
// - icon: 代理组图标（可选）
const providerGroupData = [
  {
    name: "CC CordCloud",
    provider: "cordcloud",
    url: "https://www.ccsub.org/link/f9ZZc2KlgR8rcK3T?clash=1",
    prefix: "CC | ",
    proxy: SUBSCRIPTION_PROXY,  // 使用统一配置的订阅获取代理
    type: "url-test",           // 自动选择延迟最低
    tolerance: 100,             // 长期运行优化：增加延迟容差到100ms
    interval: 600,              // 长期运行优化：增加测试间隔到10分钟
    strategy: "sticky-sessions", // 会话保持策略
    hidden: false,
    icon: "https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Siri.png",
    subscriptionInterval: 43200 // 长期运行优化：订阅更新间隔12小时
  },
  {
    name: "CT1 CreaTivity-1",
    provider: "creativity-1",
    url: "https://cady-cdnkckjkfa.cn-shenzhen.fcapp.run/api/v1/client/subscribe?token=db29f7b180ccf72e1ce3957e104a5797",
    prefix: "CT1 | ",
    proxy: SUBSCRIPTION_PROXY,  // 使用统一配置的订阅获取代理
    type: "url-test",           // 自动选择延迟最低
    tolerance: 100,             // 长期运行优化：增加延迟容差到100ms
    interval: 600,              // 长期运行优化：增加测试间隔到10分钟
    strategy: "sticky-sessions", // 会话保持策略
    hidden: false,
    icon: "https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Siri.png",
    subscriptionInterval: 43200 // 长期运行优化：订阅更新间隔12小时
  },
  {
    name: "PKM1 宝可梦-1",
    provider: "宝可梦-1",
    url: "https://52pokemon.xz61.cn/api/v1/client/subscribe?token=763e678d4a018456c5e9ede889ffb4e7",
    prefix: "PKM1 | ",
    proxy: SUBSCRIPTION_PROXY,  // 使用统一配置的订阅获取代理
    type: "url-test",           // 自动选择延迟最低
    tolerance: 50,              // 64GB内存优化：增加延迟容差到50ms
    interval: 600,              // 64GB内存优化：测试间隔10分钟
    strategy: "sticky-sessions", // 会话保持策略
    hidden: false,
    icon: "https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Siri.png"
  },
  /* {
    name: "PKM2 宝可梦-2",
    provider: "宝可梦-2",
    url: "https://52pokemon.xz61.cn/api/v1/client/subscribe?token=23191a004c093643789eb785438ce640",
    prefix: "PKM2 | ",
    proxy: SUBSCRIPTION_PROXY,  // 使用统一配置的订阅获取代理
    hidden: false,
    icon: "https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Siri.png",
    type: "url-test",
    strategy: "sticky-sessions",
    tolerance: 50,              // 64GB内存优化：增加延迟容差到50ms
    interval: 600               // 64GB内存优化：测试间隔10分钟
  }, */
  {
    name: "MLY 免流云",
    provider: "免流云",
    url: "https://mly1.543412546.xyz/api/v1/client/subscribe?token=6892b976af2bc84e12c2729d17a01372",
    prefix: "MLY | ",
    proxy: SUBSCRIPTION_PROXY,  // 使用统一配置的订阅获取代理
    hidden: false,
    icon: "https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Siri.png",
    type: "url-test",           // 自动选择延迟最低
    tolerance: 50,              // 64GB内存优化：增加延迟容差到50ms
    interval: 600,              // 64GB内存优化：测试间隔10分钟
    strategy: "sticky-sessions", // 会话保持策略
  },
  /* {
    name: "STO AMAOA",
    provider: "AMAOA",
    url: "https://sub.amaoa.xyz/storage?filename=mihomo.yaml&token=sub_ruruo",
    prefix: "STO | ",
    proxy: SUBSCRIPTION_PROXY,  // 使用统一配置的订阅获取代理
    type: "url-test",           // 自动选择延迟最低
    tolerance: 10,              // 延迟容差20ms
    interval: 300,              // 测试间隔5分钟
    strategy: "sticky-sessions", // 会话保持策略
    hidden: false,
    icon: "https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Siri.png",
  }, */
  /* {
    name: "Free-1",
    provider: "free-1",
    url: "http://ssr.cnring.shop/subscribe?token=ddfee4e3664cf6fde295098d8de421ae",
    prefix: "Free-1 | ",
    type: "select",                  // 负载均衡
    tolerance: 30,                        // 延迟容差
    interval: 300,                        // 测试间隔
    strategy: "sticky-sessions",          // 会话保持策略
    hidden: false,
    icon: "https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Star.png"
  },
  {
    name: "Free-2",
    provider: "free-2",
    url: "https://gist.githubusercontent.com/byrisk/b8954fed7476b150ccb71e41e9ed1f1e/raw/MQNODES.yaml",
    prefix: "Free-2 | ",
    type: "select",                     // 自动选择延迟最低
    tolerance: 30,                        // 延迟容差
    interval: 300,                        // 测试间隔
    strategy: "sticky-sessions",          // 会话保持策略
    proxy: SUBSCRIPTION_PROXY,                  // 使用统一配置的订阅获取代理
    hidden: false,
    icon: "https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Star.png"
  }, */
  /* {
    name: "Free-5 Alice",
    provider: "free-5",
    url: "https://alice.dns-dynamic.net/linuxdo-61adfb02-60cc-4b62-8390-cd95b91e70d1/config.yaml",
    prefix: "Free-5 | ",
    type: "url-test",                     // 自动选择延迟最低
    tolerance: 30,                        // 延迟容差
    interval: 300,                        // 测试间隔
    proxy: SUBSCRIPTION_PROXY,
    hidden: false,
    icon: "https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Star.png"
  } */
  // 完整配置示例 - 展示所有可用选项
  // {
  //   name: "DEMO 完整示例 🎯",
  //   provider: "demo-complete",
  //   url: "https://example.com/subscribe",
  //   prefix: "DEMO | ",
  //   interval: 7200,                    // 2小时更新一次
  //   proxy: "🔰 模式选择",              // 通过代理获取订阅
  //   path: "./proxy_providers/demo.yaml", // 自定义缓存路径
  //   healthCheckInterval: 600,          // 10分钟健康检查
  //   udp: true,                         // 启用UDP
  //   interfaceName: "WLAN",            // 指定网络接口（Windows示例）
  //   type: "url-test",             // 负载均衡类型
  //   strategy: "sticky-sessions",      // 会话保持策略（url-test必须配置）
  //   tolerance: 100,                   // 100ms延迟容差
  //   hidden: false,                    // 不隐藏
  //   icon: "https://cdn.jsdelivr.net/gh/Koolson/Qure@master/IconSet/Color/Available.png"
  // },
];

// ============= 辅助函数库 =============

// 获取提供商名称列表
function getProviderNames(providers) {
  return providers.map(p => p.name);
}

// 获取AI节点组名称列表
function getAIRegionalNames(aiRegionalData) {
  return aiRegionalData.map(region => region.name);
}

// AI服务配置模板
const aiServiceTemplate = {
  "type": "select",                        // 手动选择，避免AI风控
  "timeout": 10000,                        // 10秒超时
  "interval": 600,                         // 10分钟检查间隔
  "max-failed-times": 3,                   // 3次失败才切换
  // AI连接保活配置
  "connection-idle-timeout": 2400,         // 40分钟空闲超时
  "persistent-connection": true,           // 启用持久连接
  "tcp-keep-alive": true,                  // 启用TCP保活
  "keep-alive-interval": 300,              // 5分钟保活间隔
  "session-sticky": true,                  // 会话粘性
  "include-all": false
};

// Cursor专用配置模板（开发工具需要更长连接）
const cursorServiceTemplate = {
  ...aiServiceTemplate,
  "timeout": 8000,                         // 8秒超时
  "max-failed-times": 2,                   // 2次失败才切换
  "connection-idle-timeout": 3600,         // 1小时空闲超时，支持长时间开发
  "keep-alive-interval": 180               // 3分钟保活，更频繁
};

// 创建AI服务代理组
function createAIServiceGroup(name, icon, customProxies = null, template = aiServiceTemplate) {
  const defaultProxies = [
    "🎯 落地节点",                         // 首选落地节点
    ...aiRegionalNames                     // 自动包含所有AI节点组
  ];

  return {
    ...groupBaseOption,
    ...template,
    "name": name,
    "proxies": generateProxyListWithLanding(customProxies || defaultProxies, providerNames),
    "icon": icon
  };
}

// 创建特殊AI服务代理组（支持自定义首选节点）
function createSpecialAIServiceGroup(name, icon, firstChoiceProxies, template = aiServiceTemplate) {
  const proxies = [
    ...firstChoiceProxies,                 // 自定义首选节点
    "🎯 落地节点",                         // 落地节点备用
    ...aiRegionalNames                     // AI节点组
  ];

  return {
    ...groupBaseOption,
    ...template,
    "name": name,
    "proxies": generateProxyListWithLanding(proxies, providerNames),
    "icon": icon
  };
}

// 创建支持直连的AI服务代理组
function createAIServiceWithDirectGroup(name, icon, template = aiServiceTemplate) {
  const proxies = [
    "🎯 落地节点",                         // 首选落地节点
    ...aiRegionalNames,                    // AI节点组
    "🔗 直连"                              // 直连作为最后选择
  ];

  return {
    ...groupBaseOption,
    ...template,
    "name": name,
    "proxies": generateProxyListWithLanding(proxies, providerNames),
    "icon": icon
  };
}

// 创建自定义代理列表的AI服务代理组
function createCustomAIServiceGroup(name, icon, customProxies, template = aiServiceTemplate) {
  return {
    ...groupBaseOption,
    ...template,
    "name": name,
    "proxies": generateProxyListWithLanding(customProxies, providerNames),
    "icon": icon
  };
}

// 动态生成包含提供商的代理列表
function generateProxyList(baseProxies, providerNames) {
  return [...baseProxies, ...providerNames];
}

// 动态生成代理列表（考虑落地节点开关，避免循环依赖）
function generateProxyListWithLanding(baseProxies, providerNames) {
  // 如果启用落地节点，在基础代理列表的开头添加落地节点
  const proxiesWithLanding = ENABLE_LANDING_NODES
    ? ["🎯 落地节点", ...baseProxies.filter(proxy => proxy !== "🎯 落地节点")]
    : baseProxies.filter(proxy => proxy !== "🎯 落地节点");

  return [...proxiesWithLanding, ...providerNames];
}

// 定义会被落地节点引用的代理组名称（这些组必须在落地节点创建之前完成，且不能包含落地节点）
const LANDING_REFERENCED_GROUPS = [
  "⚡ 深港专线",
  "🚄 3.0倍率",
  "🇺🇸 美国",
  "🏠 家宽"
  // 注意：AI组不被落地节点引用，所以不需要添加到这里
];

// 区域代理组生成器
function createRegionalProxyGroups(regionalData, providerData, providerNames) {
  return regionalData.map(region => {
    // 检查是否被落地节点引用
    const isReferencedByLanding = LANDING_REFERENCED_GROUPS.includes(region.name);

    const baseConfig = {
      ...groupBaseOption,
      type: region.type || "url-test",
      strategy: region.strategy || undefined,
      "include-all": false,
      "use": providerNames.length > 0 ? providerNames.map(name => {
        const provider = providerData.find(p => p.name === name);
        return provider ? provider.provider : null;
      }).filter(p => p !== null) : [],
      ...region
    };

    // 如果被落地节点引用，添加额外的安全过滤
    if (isReferencedByLanding) {
      // 合并现有的exclude-filter和落地节点安全过滤
      const existingFilter = region["exclude-filter"] || "";
      const landingFilter = "(?i)(landing|落地|webshare)";
      baseConfig["exclude-filter"] = existingFilter
        ? `(${existingFilter})|(${landingFilter})`
        : landingFilter;
    }

    return baseConfig;
  });
}

// 速率代理组生成器
function createSpeedProxyGroups(speedData, providerData, providerNames) {
  return speedData.map(speed => {
    // 检查是否被落地节点引用
    const isReferencedByLanding = LANDING_REFERENCED_GROUPS.includes(speed.name);

    const baseConfig = {
      ...groupBaseOption,
      type: speed.type || "url-test",
      strategy: speed.strategy || undefined,
      "include-all": false,
      "use": providerNames.length > 0 ? providerNames.map(name => {
        const provider = providerData.find(p => p.name === name);
        return provider ? provider.provider : null;
      }).filter(p => p !== null) : [],
      ...speed
    };

    // 如果被落地节点引用，添加额外的安全过滤
    if (isReferencedByLanding) {
      // 合并现有的exclude-filter和落地节点安全过滤
      const existingFilter = speed["exclude-filter"] || "";
      const landingFilter = "(?i)(landing|落地|webshare)";
      baseConfig["exclude-filter"] = existingFilter
        ? `(${existingFilter})|(${landingFilter})`
        : landingFilter;
    }

    return baseConfig;
  });
}

// 生成提供商配置 - 长期运行优化版本
function generateProxyProviders(providers) {
  const providers_config = {};

  providers.forEach(provider => {
    // 基础配置 - 长期运行优化
    const config = {
      "type": "http",
      "url": provider.url,
      "interval": provider.subscriptionInterval || 21600, // 长期运行优化：默认6小时更新间隔
      "timeout": 60000,                                   // 长期运行优化：增加超时到60秒
      "proxy": provider.proxy || "DIRECT",               // 支持通过代理获取订阅
      "override": {
        "additional-prefix": provider.prefix,
        "udp": provider.udp !== undefined ? provider.udp : true, // 支持自定义UDP设置
        "skip-cert-verify": false,                        // 长期运行优化：启用证书验证提高安全性
        "tcp-no-delay": true,                            // 长期运行优化：禁用Nagle算法
        "tcp-keep-alive": true                           // 长期运行优化：启用TCP保活
      },
      "health-check": {
        "enable": true,
        "url": "http://www.gstatic.com/generate_204",
        "interval": provider.healthCheckInterval || 600,  // 优化：10分钟健康检查，快速发现问题节点
        "timeout": 8000,                                 // 优化：8秒超时，平衡检测和稳定性
        "lazy": false,                                   // 优化：关闭懒加载，主动检测
        "expected-status": 204,                          // 期望状态码
        "max-failed-times": 3                            // 3次失败后标记为不可用
      }
    };

    // 如果指定了缓存路径，添加path配置
    if (provider.path) {
      config.path = provider.path;
    }

    // 如果指定了接口名称，添加到override中
    if (provider.interfaceName) {
      config.override["interface-name"] = provider.interfaceName;
    }

    providers_config[provider.provider] = config;
  });

  return providers_config;
}

// 获取提供商名称列表
const providerNames = getProviderNames(providerGroupData);

// 获取AI节点组名称列表
const aiRegionalNames = getAIRegionalNames(regionalGroupData_AI);

// ============= 先创建被落地节点引用的基础代理组 =============

// 根据区域数据生成代理组配置（通用 + AI）
const regionalProxyGroups = createRegionalProxyGroups(regionalGroupData, providerGroupData, providerNames);
const regionalProxyGroups_AI = createRegionalProxyGroups(regionalGroupData_AI, providerGroupData, providerNames);

// 根据速率数据生成代理组配置
const speedProxyGroups = createSpeedProxyGroups(speedGroupData, providerGroupData, providerNames);

// ============= 然后创建落地节点（现在可以安全引用上述组） =============

// Add custom home broadband node (直连，无循环依赖) - 延迟优化版
const customHomeProxy = {
  name: "🎯 Webshare-US专线-Direct",
  type: "socks5",
  server: landingNodeConfig.server,
  port: landingNodeConfig.port,
  username: landingNodeConfig.username,
  password: landingNodeConfig.password,
  udp: true,
  "dialer-proxy": "🔗 直连",
  "skip-cert-verify": true,  // 跳过证书验证，减少延迟
  "tfo": true,               // 启用TCP Fast Open
  hidden: false              // 改为false，方便测试延迟
};

// Add VLESS Reality node (直连，最新协议)
const vlessRealityProxy = {
  name: "🇺🇸 VLESS-Reality-Direct",
  type: "vless",
  server: "***********",
  port: 37571,
  uuid: "8d4b9782-538f-47c3-a725-11d1655f8da5",
  flow: "xtls-rprx-vision",
  network: "tcp",
  tls: true,
  "reality-opts": {
    "public-key": "OXHOn9V-I0RNYm_jYescBGgjaDfHvgMAsFNOwM2-ZHk",
    "short-id": ""
  },
  "servername": "www.iij.ad.jp",
  "client-fingerprint": "chrome",
  udp: true,
  "dialer-proxy": "🔗 直连",
  hidden: true
};

// Add Hysteria2 node (直连，高速协议)
const hysteria2Proxy = {
  name: "🇺🇸 Hysteria2-Direct",
  type: "hysteria2",
  server: "***********",
  port: 37574,
  password: "8d4b9782-538f-47c3-a725-11d1655f8da5",
  sni: "www.bing.com",
  "skip-cert-verify": true,
  alpn: ["h3"],
  udp: true,
  "dialer-proxy": "🔗 直连",
  hidden: true
};

// Add TUIC node (直连，QUIC协议)
const tuicProxy = {
  name: "🇺🇸 TUIC-Direct",
  type: "tuic",
  server: "***********",
  port: 37573,
  uuid: "8d4b9782-538f-47c3-a725-11d1655f8da5",
  password: "kpuSGR6oypGgYrSszISd10et",
  sni: "www.bing.com",
  "skip-cert-verify": true,
  alpn: ["h3"],
  "congestion-controller": "bbr",
  "udp-relay-mode": "native",
  udp: true,
  "dialer-proxy": "🔗 直连",
  hidden: true
};

// Add VLESS Reality HK relay variant
const vlessRealityHKProxy = {
  name: "🇺🇸 VLESS-Reality-HK-Relay",
  type: "vless",
  server: "***********",
  port: 37571,
  uuid: "8d4b9782-538f-47c3-a725-11d1655f8da5",
  flow: "xtls-rprx-vision",
  network: "tcp",
  tls: true,
  "reality-opts": {
    "public-key": "OXHOn9V-I0RNYm_jYescBGgjaDfHvgMAsFNOwM2-ZHk",
    "short-id": ""
  },
  "servername": "www.iij.ad.jp",
  "client-fingerprint": "chrome",
  udp: true,
  "dialer-proxy": "⚡ 深港专线",
  hidden: true
};

// Add chained proxy using HK-SZ line as transit
const chainedHomeProxy = {
  name: "🔗 Landing-HK-Relay", // 修改名称避免循环依赖
  type: "socks5",
  server: landingNodeConfig.server,
  port: landingNodeConfig.port,
  username: landingNodeConfig.username,
  password: landingNodeConfig.password,
  udp: true,
  "dialer-proxy": "⚡ 深港专线",
  hidden: true // 确保不被其他代理组自动包含
};

// Add auto-select fastest transit proxy
const autoFastestTransitProxy = {
  name: "🚀 Landing-Auto-Relay", // 修改名称避免循环依赖
  type: "socks5",
  server: landingNodeConfig.server,
  port: landingNodeConfig.port,
  username: landingNodeConfig.username,
  password: landingNodeConfig.password,
  udp: true,
  "dialer-proxy": "⚡ 深港专线",
  hidden: true // 确保不被其他代理组自动包含
};

// Add regional transit options for testing
const hkTransitProxy = {
  name: "🚄 Landing-Fast-Relay", // 修改名称避免循环依赖
  type: "socks5",
  server: landingNodeConfig.server,
  port: landingNodeConfig.port,
  username: landingNodeConfig.username,
  password: landingNodeConfig.password,
  udp: true,
  "dialer-proxy": "🚄 3.0倍率",
  hidden: true // 确保不被其他代理组自动包含
};

const usTransitProxy = {
  name: "🇺🇸 Landing-US-Relay", // 修改名称避免循环依赖
  type: "socks5",
  server: landingNodeConfig.server,
  port: landingNodeConfig.port,
  username: landingNodeConfig.username,
  password: landingNodeConfig.password,
  udp: true,
  "dialer-proxy": "🇺🇸 美国",
  hidden: true // 确保不被其他代理组自动包含
};

const homeTransitProxy = {
  name: "🏠 Landing-Home-Relay", // 修改名称避免循环依赖
  type: "socks5",
  server: landingNodeConfig.server,
  port: landingNodeConfig.port,
  username: landingNodeConfig.username,
  password: landingNodeConfig.password,
  udp: true,
  "dialer-proxy": "🏠 家宽",
  hidden: true // 确保不被其他代理组自动包含
};


// 根据开关决定是否添加落地节点（包含所有转接节点）
const landingNodes = ENABLE_LANDING_NODES ? [
  customHomeProxy,
  vlessRealityProxy,
  hysteria2Proxy,
  tuicProxy,
  vlessRealityHKProxy,
  chainedHomeProxy,
  autoFastestTransitProxy,
  hkTransitProxy,
  usTransitProxy,
  homeTransitProxy
] : [];

// 落地节点组配置（仅在启用时添加）
const landingNodeGroup = ENABLE_LANDING_NODES ? [{
  ...groupBaseOption,
  "name": "🎯 落地节点",
  "type": "select",
  "proxies": [
    "🎯 Webshare-US专线-Direct",
    "🇺🇸 VLESS-Reality-Direct",
    "🇺🇸 Hysteria2-Direct",
    "🇺🇸 TUIC-Direct",
    "🇺🇸 VLESS-Reality-HK-Relay",
    "🔗 Landing-HK-Relay",
    "🚀 Landing-Auto-Relay",
    "🚄 Landing-Fast-Relay",
    "🇺🇸 Landing-US-Relay",
    "🏠 Landing-Home-Relay"
  ], // 包含所有落地节点
  "include-all": false,
  "icon": "https://fastly.jsdelivr.net/gh/yz0812/mypic@master/Clash_Verge_Rev/10007.svg"
}] : [];

// 根据提供商数据生成代理组配置
const providerProxyGroups = providerGroupData.map(provider => ({
  ...groupBaseOption,
  type: provider.type || "url-test", // 使用提供商配置的type，默认为url-test
  strategy: provider.strategy || undefined, // 使用提供商配置的strategy
  tolerance: provider.tolerance || 50, // 使用提供商配置的tolerance，默认50ms
  interval: provider.interval || groupBaseOption.interval, // 使用提供商配置的interval
  "include-all": false, // 提供商组不包含所有节点，只使用指定的提供商
  use: [provider.provider],
  name: provider.name,
  hidden: provider.hidden,
  icon: provider.icon
}));

const proxyGroupsConfig = [
  // 主要选择组
  {
    ...groupBaseOption,
    "name": "🔰 模式选择",
    "type": "select",
    "proxies": [
      "⚙️ 自动选择",
      "🔗 直连"
    ]
  },

  // 速率代理组（恢复原位置）
  ...speedProxyGroups,

  // 🇺🇸 美国节点（单独提取，放在3.0倍率后面）
  ...regionalProxyGroups.filter(group => group.name === "🇺🇸 美国"),

  // 🇺🇸 美国-AI节点（单独提取，放在落地节点上方）
  ...regionalProxyGroups_AI.filter(group => group.name === "🇺🇸 美国-AI"),

  // 落地节点组（根据开关动态添加）
  ...landingNodeGroup,

  // 🇭🇰 香港节点（单独提取，放在自动选择前面）
  ...regionalProxyGroups.filter(group => group.name === "🇭🇰 香港"),

  {
    ...groupBaseOption,
    "name": "⚙️ 自动选择",
    "type": "select",
    "proxies": generateProxyListWithLanding([
      "🇭🇰 香港",                           // 首选香港节点
      "⚡ 深港专线",
      "🚄 3.0倍率",
      // ✅ 自动包含其他通用节点组（排除香港避免重复）
      ...regionalGroupData.filter(region => region.name !== "🇭🇰 香港").map(region => region.name)
    ], providerNames),
    "include-all": false,
    "icon": "https://fastly.jsdelivr.net/gh/yz0812/mypic@master/Clash_Verge_Rev/10002.svg"
  },
  // 服务专用组
  {
    ...groupBaseOption,
    "name": "🥰 广告拦截",
    "type": "select",
    "proxies": ["REJECT", "DIRECT"],
    "include-all": false,
    "hidden": true,                                                          // ✅ 隐藏显示，但保持功能
    "icon": "https://fastly.jsdelivr.net/gh/yz0812/mypic@master/Clash_Verge_Rev/10009.svg"
  },
  {
    ...groupBaseOption,
    "name": "🔗 直连",
    "type": "select",
    "proxies": ["DIRECT"],
    "include-all": false,
    "hidden": true,                                                          // ✅ 隐藏显示，但保持功能
    "icon": "https://fastly.jsdelivr.net/gh/yz0812/mypic@master/Clash_Verge_Rev/10041.svg"
  },
  // ✅ 使用函数简化：Copilot（支持直连）
  createSpecialAIServiceGroup(
    "💻 Copilot",
    "https://fastly.jsdelivr.net/gh/yz0812/mypic@master/Clash_Verge_Rev/github-copilot.svg",
    ["🔗 直连"]  // 首选直连
  ),
  // ✅ 使用函数简化：Gemini
  createAIServiceGroup(
    "✨ Gemini",
    "https://fastly.jsdelivr.net/gh/yz0812/mypic@master/Clash_Verge_Rev/gemini.png"
  ),
  // ✅ 使用函数简化：OpenAI
  createAIServiceGroup(
    "🤖 OpenAI",
    "https://fastly.jsdelivr.net/gh/yz0812/mypic@master/Clash_Verge_Rev/10028.svg"
  ),
  // ✅ 使用函数简化：Grok（支持直连备用）
  createAIServiceWithDirectGroup(
    "🤖 Grok",
    "https://fastly.jsdelivr.net/gh/yz0812/mypic@master/Clash_Verge_Rev/grok.svg"
  ),
  // ✅ 使用函数简化：Augment（默认使用日本节点）
  createCustomAIServiceGroup(
    "🚀 Augment",
    "https://fastly.jsdelivr.net/gh/yz0812/mypic@master/Clash_Verge_Rev/10028.svg",
    [
      "🇯🇵 日本-AI",                        // 首选日本AI节点
      "🎯 落地节点",                         // 落地节点备用
      "⚡ 深港专线",                         // 深港专线备用
      ...aiRegionalNames.filter(name => !name.includes("日本")), // 其他AI节点组（排除日本避免重复）
      "🔗 直连"                              // 直连作为最后选择
    ]
  ),
  {
    ...groupBaseOption,
    "name": "💻 Cursor",
    "type": "select",                        // ✅ 保持手动选择，避免开发中断
    "timeout": 8000,                         // 保持8秒超时
    "interval": 600,                         // ✅ 修复：改为10分钟检查
    "max-failed-times": 2,                   // ✅ 修复：改为2次失败才切换
    "proxies": [
      "🇸🇬 新加坡-AI",                      // 首选新加坡AI，对AI友好
      "🎯 落地节点",                         // 落地节点备用
      // ✅ 自动包含其他AI节点组
      ...aiRegionalNames.filter(name => name !== "🇸🇬 新加坡-AI"),
      ...providerNames                       // 添加提供商节点
    ],
    // ✅ 新增：Cursor专用连接保活配置（开发工具需要更长连接）
    "connection-idle-timeout": 3600,         // 1小时空闲超时，支持长时间开发
    "persistent-connection": true,           // 启用持久连接
    "tcp-keep-alive": true,                  // 启用TCP保活
    "keep-alive-interval": 180,              // 3分钟保活，更频繁
    "session-sticky": true,                  // 会话粘性
    "max-connection-age": 10800,             // 3小时最大连接存活
    "connection-reuse-limit": 1000,          // 高连接复用限制
    "include-all": false,
    "icon": "https://fastly.jsdelivr.net/gh/yz0812/mypic@master/Clash_Verge_Rev/cursor.svg"
  },
  // ✅ 使用函数简化：Claude
  createAIServiceGroup(
    "🧠 Claude",
    "https://fastly.jsdelivr.net/gh/yz0812/mypic@master/Clash_Verge_Rev/claude.png"
  ),
  {
    ...groupBaseOption,
    "name": "📱 GitHub",
    "type": "select",
    "proxies": generateProxyListWithLanding([
      "⚙️ 自动选择",
      "🎯 落地节点",
      "⚡ 深港专线",
      // ✅ 自动包含所有通用区域节点组
      ...regionalGroupData.map(region => region.name),
      "🔗 直连"
    ], providerNames),
    "include-all": false,
    "icon": "https://fastly.jsdelivr.net/gh/yz0812/mypic@master/Clash_Verge_Rev/10001.svg"
  },
  {
    ...groupBaseOption,
    "name": "📢 Google",
    "type": "select",                        // ✅ 改为手动选择模式
    "timeout": 5000,                         // 5秒超时
    "proxies": generateProxyListWithLanding([
      "⚙️ 自动选择",                         // 首选自动选择
      "⚡ 深港专线",                         // 优先高速专线
      // ✅ 自动包含所有通用区域节点组
      ...regionalGroupData.map(region => region.name)
    ], providerNames),
    "include-all": false,
    "icon": "https://fastly.jsdelivr.net/gh/yz0812/mypic@master/Clash_Verge_Rev/10020.svg"
  },
  {
    ...groupBaseOption,
    "name": "🎬 Netflix",
    "type": "select",
    "proxies": generateProxyListWithLanding([
      "⚙️ 自动选择",
      "🎯 落地节点",
      // ✅ 自动包含所有通用区域节点组
      ...regionalGroupData.map(region => region.name),
      "🔗 直连"
    ], providerNames),
    "include-all": false,
    "icon": "https://fastly.jsdelivr.net/gh/yz0812/mypic@master/Clash_Verge_Rev/10026.svg"
  },
  {
    ...groupBaseOption,
    "name": "🎮 游戏服务",
    "type": "select",
    "proxies": generateProxyListWithLanding([
      "🔗 直连",
      "⚙️ 自动选择",
      "⚡ 深港专线",
      // ✅ 自动包含所有通用区域节点组
      ...regionalGroupData.map(region => region.name)
    ], providerNames),
    "include-all": false,
    "icon": "https://github.com/DustinWin/ruleset_geodata/releases/download/icons/games-cn.png"
  },
  {
    ...groupBaseOption,
    "name": "Ⓜ️ Microsoft",
    "type": "select",
    "proxies": generateProxyListWithLanding([
      "🔗 直连",
      "⚙️ 自动选择",
      // ✅ 自动包含所有通用区域节点组
      ...regionalGroupData.map(region => region.name)
    ], providerNames),
    "include-all": false,
    "icon": "https://fastly.jsdelivr.net/gh/yz0812/mypic@master/Clash_Verge_Rev/10014.svg"
  },
  {
    ...groupBaseOption,
    "name": "☁️ OneDrive",
    "type": "select",
    "proxies": generateProxyListWithLanding([
      "🔗 直连",
      "⚙️ 自动选择",
      // ✅ 自动包含所有通用区域节点组
      ...regionalGroupData.map(region => region.name)
    ], providerNames),
    "include-all": false,
    "icon": "https://fastly.jsdelivr.net/gh/yz0812/mypic@master/Clash_Verge_Rev/10040.svg"
  },
  {
    ...groupBaseOption,
    "name": "🍎 苹果服务",
    "type": "select",
    "proxies": generateProxyListWithLanding([
      "🔗 直连",
      "⚙️ 自动选择",
      // ✅ 自动包含所有通用区域节点组
      ...regionalGroupData.map(region => region.name)
    ], providerNames),
    "include-all": false,
    "icon": "https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/apple.svg"
  },
  {
    ...groupBaseOption,
    "name": "📲 电报消息",
    "type": "select",
    "proxies": generateProxyListWithLanding([
      "⚙️ 自动选择",
      "⚡ 深港专线",
      // ✅ 自动包含所有通用区域节点组
      ...regionalGroupData.map(region => region.name),
      "🔗 直连"
    ], providerNames),
    "include-all": false,
    "icon": "https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/telegram.svg"
  },

  // ✅ 其他通用区域代理组（排除🇺🇸 美国和🇭🇰 香港，因为已经单独放在前面了）
  ...regionalProxyGroups.filter(group => group.name !== "🇺🇸 美国" && group.name !== "🇭🇰 香港"),

  // ✅ AI节点组（放在漏网之鱼上面显示，排除美国-AI避免重复）
  ...regionalProxyGroups_AI.filter(group => group.name !== "🇺🇸 美国-AI"),

  {
    ...groupBaseOption,
    "name": "🐟 漏网之鱼",
    "type": "select",
    "proxies": generateProxyListWithLanding([
      "⚙️ 自动选择",
      "🎯 落地节点",
      "⚡ 深港专线",
      // ✅ 自动包含所有通用区域节点组
      ...regionalGroupData.map(region => region.name),
      "🔗 直连"
    ], providerNames),
    "include-all": false,
    "icon": "https://fastly.jsdelivr.net/gh/clash-verge-rev/clash-verge-rev.github.io@main/docs/assets/icons/fish.svg"
  },

  // 配置提供商代理组
  ...providerProxyGroups
];

// 多订阅合并配置
const proxyProviders = generateProxyProviders(providerGroupData);

// 配置文件管理
const profileConfig = {
  "store-selected": true,    // 保存用户选择的代理配置
  "store-fake-ip": true      // 保存 Fake IP 映射关系
};

// 域名嗅探配置
const snifferConfig = {
  "enable": true,
  "parse-pure-ip": true,      // 解析纯IP请求
  "sniff": {
    "TLS": {
      "ports": [443, 8443]    // HTTPS端口
    },
    "HTTP": {
      "ports": [80, "8080-8880"],
      "override-destination": true
    },
    "QUIC": {
      "ports": [443, 8443]    // QUIC协议端口
    }
  },
  "skip-domain": [
    "Mijia Cloud",           // 小米智能家居
    "+.apple.com",           // 苹果服务
    "+.icloud.com"           // iCloud服务
  ]
};

// GeoData 配置
const geodataConfig = {
  "geodata-mode": true,
  "geo-auto-update": true,
  "geo-update-interval": 24,
  "geodata-loader": "standard",
  "geox-url": {
    "geoip": "https://fastgh.lainbo.com/https://github.com/MetaCubeX/meta-rules-dat/releases/download/latest/geoip-lite.dat",
    "geosite": "https://fastgh.lainbo.com/https://github.com/MetaCubeX/meta-rules-dat/releases/download/latest/geosite.dat",
    "mmdb": "https://fastgh.lainbo.com/https://github.com/MetaCubeX/meta-rules-dat/releases/download/latest/country-lite.mmdb",
    "asn": "https://fastgh.lainbo.com/https://github.com/xishang0128/geoip/releases/download/latest/GeoLite2-ASN.mmdb"
  }
};

// TUN 模式配置 - 64GB内存优化，AI风控防护
const tunConfig = {
  "enable": true,           // 默认关闭，需要时可开启
  "stack": "mixed",
  "auto-route": true,
  "auto-redirect": true,
  "auto-detect-interface": true,
  "dns-hijack": ["any:53", "tcp://any:53"],
  "device": "mihomo",
  "mtu": 1400,                         // ✅ 修复：改为1400，避免分片，防止AI风控
  "strict-route": true,                // ✅ 保持：严格路由，防止AI风控
  "gso": true,
  "gso-max-size": 524288,              // 64GB内存优化：增加GSO到512KB
  "udp-timeout": 60,                   // ✅ 修复：改为60秒，避免连接堆积
  "tcp-timeout": 600,                  // ✅ 修复：改为10分钟，平衡稳定性
  "stack-buffer-size": 1048576,        // 64GB内存优化：增加栈缓冲区到1MB
  "endpoint-independent-nat": true,    // ✅ 修复：启用端点无关NAT
  "route-address": [                   // 路由地址优化
    "0.0.0.0/1",
    "*********/1",
    "::/1",
    "8000::/1"
  ],
  "route-exclude-address": [           // 排除本地地址
    "***********/16",
    "10.0.0.0/8",
    "**********/12",
    "*********/8",
    "***********/16",
    "*********/4",
    "::1/128",
    "fc00::/7",
    "fe80::/10",
    "ff00::/8"
  ],
  "include-android-user": [0],         // Android用户包含
  "include-package": [],               // 包含的应用包
  "exclude-package": []                // 排除的应用包
};

// 64GB内存优化配置 - 充分利用大内存优势，提高长期运行稳定性
const cacheConfig = {
  "profile-cache-size": 2048,              // 64GB内存优化：增加到2GB配置文件缓存
  "rule-cache-size": 1024,                 // 64GB内存优化：增加到1GB规则缓存
  "geoip-cache-size": 512,                 // 64GB内存优化：增加到512MB GeoIP缓存
  "dns-cache-size": 256,                   // 64GB内存优化：增加到256MB DNS缓存
  "connection-cache-size": 512,            // ✅ 修复：增加连接缓存到512MB，充分利用大内存
  "proxy-cache-size": 64,                  // 64GB内存优化：增加代理缓存到64MB
  "enable-statistics": true,               // 启用统计
  "statistics-interval": 600,              // 64GB内存优化：减少统计频率到10分钟
  "enable-memory-monitor": true,           // 内存监控
  "memory-limit": 8192,                    // ✅ 保持：8GB内存限制，充分利用64GB内存
  "enable-connection-monitor": true,       // 连接监控
  "enable-performance-monitor": true,      // 64GB内存优化：启用性能监控
  "cache-cleanup-interval": 900,           // ✅ 修复：改为15分钟清理，提高长期稳定性
  "adaptive-memory": true,                 // 自适应内存管理
  "aggressive-gc": false,                  // 64GB内存优化：关闭激进垃圾回收，减少性能影响
  "memory-pressure-threshold": 85          // ✅ 保持：85%内存压力时才清理，大内存环境合适
};

// 64GB内存性能优化配置
const performanceConfig = {
  "unified-delay": true,                    // 统一延迟计算
  "tcp-concurrent": true,                   // TCP并发连接
  "find-process-mode": "always",           // 64GB内存优化：启用进程查找，内存充足
  "global-client-fingerprint": "chrome",   // Chrome指纹伪装
  "keep-alive-interval": 600,              // 64GB内存优化：增加保活间隔到10分钟
  "tcp-keep-alive": true,                  // 启用TCP保活
  "tcp-no-delay": true,                    // 禁用Nagle算法，降低延迟
  "tcp-fast-open": true,                   // 启用TCP Fast Open
  "socket-mark": 0,                        // Socket标记（Linux）
  "connection-pool-size": 128,             // ✅ 修复：改为128个连接池，更保守
  "connection-idle-timeout": 600,          // 64GB内存优化：增加空闲超时到10分钟
  "keep-alive-interval": 300,              // ✅ 新增：改为5分钟保活间隔
  "max-concurrent-streams": 100,           // ✅ 修复：改为100个并发流，更保守
  "max-open-files": 65536,                 // 64GB内存优化：增加最大打开文件数
  "read-buffer-size": 131072,              // 64GB内存优化：增加读缓冲区到128KB
  "write-buffer-size": 131072,             // 64GB内存优化：增加写缓冲区到128KB
  "http2-settings": {                      // HTTP/2专用设置 - 64GB内存优化
    "header-table-size": 65536,            // ✅ 修复：改为64KB头部表大小
    "enable-push": false,                  // ✅ 修复：关闭服务器推送，减少复杂性
    "max-concurrent-streams": 100,         // ✅ 修复：改为100个并发流
    "initial-window-size": 1048576,        // ✅ 修复：改为1MB初始窗口
    "max-frame-size": 32768,               // 32KB最大帧大小
    "ping-timeout": 30,                    // ✅ 新增：30秒ping超时
    "ping-interval": 60                    // ✅ 新增：1分钟ping间隔，保持连接活跃
  },
  "external-controller-cors": {
    "allow-private-network": true,
    "allow-origins": ["*"]
  },
  ...cacheConfig,                          // 合并缓存配置
  // 64GB内存高级优化
  "memory-optimization": {
    "enable-memory-pool": true,            // 启用内存池
    "memory-pool-size": 2048,              // 64GB内存优化：增加内存池大小到2GB
    "enable-zero-copy": true,              // 启用零拷贝
    "enable-splice": true,                 // 启用splice优化
    "prefetch-dns": true,                  // 64GB内存优化：启用DNS预取，提高响应速度
    "prefetch-connections": 32,            // 64GB内存优化：增加预取连接数到32个
    "aggressive-gc": false,                // 64GB内存优化：关闭激进GC，减少性能影响
    "memory-pressure-threshold": 90,       // 64GB内存优化：提高内存压力阈值到90%
    "adaptive-buffer-size": true,          // 自适应缓冲区大小
    "smart-memory-management": true,       // 智能内存管理
    "auto-cleanup-interval": 3600,         // 64GB内存优化：自动清理间隔1小时
    "connection-reuse-limit": 500          // 64GB内存优化：增加连接复用限制到500
  }
};

// 端口配置 - 长期运行优化
const portConfig = {
  "mixed-port": 4570,                      // 混合端口（HTTP+SOCKS5）
  "allow-lan": true,                       // 允许局域网连接
  "bind-address": "*",                     // 绑定所有地址
  "log-level": "warning",                  // 长期运行优化：减少日志级别到warning
  "log-file": "./logs/clash.log",         // 添加日志文件路径
  "log-max-size": 50,                     // 日志文件最大50MB
  "log-max-backups": 3,                   // 保留3个备份文件
  "log-max-age": 7,                       // 日志保留7天
  "ipv6": true,                           // 启用IPv6
  "mode": "rule"                          // 规则模式
};

// 长期运行监控和自动清理配置
const longTermConfig = {
  "auto-restart": {
    "enable": false,                       // 自动重启功能（需要外部脚本支持）
    "memory-threshold": 95,                // 64GB内存优化：内存使用率超过95%时重启
    "uptime-threshold": 604800,            // 64GB内存优化：运行时间超过7天时重启
    "connection-threshold": 50000          // 64GB内存优化：连接数超过50000时重启
  },
  "log-rotation": {
    "enable": true,                        // 启用日志轮转
    "max-size": "50MB",                    // 单个日志文件最大50MB
    "max-files": 3,                        // 最多保留3个日志文件
    "compress": true                       // 压缩旧日志文件
  },
  "health-check": {
    "enable": true,                        // 启用健康检查
    "interval": 3600,                      // 64GB内存优化：1小时检查一次
    "memory-check": true,                  // 检查内存使用
    "connection-check": true,              // 检查连接数
    "dns-check": true                      // 检查DNS解析
  },
  "cleanup": {
    "enable": true,                        // 启用自动清理
    "interval": 7200,                      // 64GB内存优化：2小时清理一次
    "clear-dns-cache": false,              // 64GB内存优化：不清理DNS缓存，保持性能
    "clear-connection-cache": false,       // 64GB内存优化：不清理连接缓存，保持性能
    "clear-rule-cache": false,             // 不清理规则缓存（影响性能）
    "gc-force": false                      // 64GB内存优化：不强制垃圾回收
  }
};

// 程序入口
function main(config) {
  const originalProxies = config?.proxies ? [...config.proxies] : [];
  const proxyCount = originalProxies.length;
  const originalProviders = config?.["proxy-providers"] || {};
  const proxyProviderCount = originalProviders !== null && typeof originalProviders === 'object' ? Object.keys(originalProviders).length : 0;

  if (proxyCount === 0 && proxyProviderCount === 0) {
    throw new Error("配置文件中未找到任何代理");
  }

  // 应用基础配置
  config["dns"] = dnsConfig;
  config["rule-providers"] = ruleProviders;
  config["rules"] = rules;

  // 应用实用性配置
  config["profile"] = profileConfig;
  config["sniffer"] = snifferConfig;
  Object.assign(config, geodataConfig);
  Object.assign(config, performanceConfig);
  Object.assign(config, portConfig);

  // 长期运行优化配置
  config["long-term-optimization"] = longTermConfig;

  // 添加运行时监控配置
  config["runtime-monitor"] = {
    "enable": true,
    "memory-warning-threshold": 85,        // 64GB内存优化：内存使用率85%时警告
    "memory-critical-threshold": 95,       // 64GB内存优化：内存使用率95%时紧急清理
    "connection-warning-threshold": 20000, // 64GB内存优化：连接数20000时警告
    "uptime-check-interval": 7200,         // 64GB内存优化：每2小时检查运行时间
    "auto-gc-interval": 7200,              // 64GB内存优化：每2小时强制垃圾回收
    "log-performance-stats": true          // 记录性能统计
  };

  // 添加稳定性增强配置
  config["stability-enhancements"] = {
    "enable-connection-reuse-limit": true,  // 启用连接复用限制
    "connection-reuse-max": 100,            // 单个连接最大复用次数
    "enable-memory-pressure-detection": true, // 启用内存压力检测
    "memory-pressure-action": "cleanup",    // 内存压力时的动作
    "enable-automatic-restart": false,      // 禁用自动重启（由外部监控处理）
    "connection-leak-detection": true,      // 启用连接泄漏检测
    "dns-cache-auto-cleanup": true,         // 启用DNS缓存自动清理
    "rule-cache-validation": true,          // 启用规则缓存验证
    "proxy-health-auto-recovery": true      // 启用代理健康自动恢复
  };

  // 添加错误恢复配置
  config["error-recovery"] = {
    "enable": true,
    "max-retry-attempts": 3,               // 最大重试次数
    "retry-delay": 5000,                   // 重试延迟(毫秒)
    "circuit-breaker-threshold": 5,        // 熔断器阈值
    "circuit-breaker-timeout": 30000,      // 熔断器超时(毫秒)
    "auto-fallback": true,                 // 启用自动回退
    "fallback-proxy": "DIRECT",            // 回退代理
    "health-check-on-failure": true        // 失败时进行健康检查
  };

  // TUN 模式配置（可选启用）
  // config["tun"] = tunConfig;  // 取消注释以启用 TUN 模式

  // Process original proxies (just ensure UDP)
  const processedProxies = originalProxies.map(proxy => {
    if (proxy && typeof proxy === 'object' && proxy.name) {
      proxy.udp = true;

      // 节点绑定的接口，从此接口发起连接，适用于部分vpn情况
      // proxy["interface-name"] = "WLAN"
      // proxy["interface-name"] = "以太网"
    } else {
      // 发现一个无效或缺少名称的原始代理配置
      return null;
    }
    return proxy;
  }).filter(p => p !== null);

  // Use processed proxies with optional landing nodes
  config["proxies"] = [...processedProxies, ...landingNodes];
  config["proxy-providers"] = {
    ...originalProviders,
    ...proxyProviders
  };

  // 长期运行额外优化配置
  config["experimental"] = {
    "ignore-resolve-fail": true,           // 忽略解析失败，提高稳定性
    "sniff-tls-sni": true,                // 嗅探TLS SNI
    "fingerprint": "chrome",              // 使用Chrome指纹
    "quic-go-disable-gso": false,         // 启用QUIC GSO优化
    "quic-go-disable-ecn": false,         // 启用QUIC ECN
    "dialer-ip-version": "dual",          // 双栈IP版本
    "tcp-concurrent": true,               // TCP并发连接
    "interface-name": "",                 // 自动选择网络接口
    "routing-mark": 0                     // 路由标记
  };

  // 长期运行监控和自动维护
  config["maintenance"] = {
    "enable-auto-gc": true,               // 启用自动垃圾回收
    "gc-interval": 7200,                  // 64GB内存优化：2小时执行一次GC
    "enable-connection-cleanup": true,    // 启用连接清理
    "cleanup-interval": 3600,             // 64GB内存优化：1小时清理一次无效连接
    "enable-memory-monitor": true,        // 启用内存监控
    "memory-check-interval": 1800,        // 64GB内存优化：30分钟检查一次内存
    "enable-performance-log": true,       // 64GB内存优化：启用性能日志
    "enable-health-check": true,          // 启用健康检查
    "health-check-interval": 7200         // 64GB内存优化：2小时执行一次全面健康检查
  };

  config["proxy-groups"] = proxyGroupsConfig;

  return config;
}



